apiVersion: apps/v1
kind: Deployment
metadata:
  name: jitsi-web
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: web
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: jitsi
      app.kubernetes.io/component: web
  template:
    metadata:
      labels:
        app.kubernetes.io/name: jitsi
        app.kubernetes.io/component: web
    spec:
      containers:
        - name: web
          image: jitsi/web:stable-8719
          ports:
            - containerPort: 80
              name: http
            - containerPort: 443
              name: https
          env:
            - name: XMPP_DOMAIN
              value: "meet.jitsi"
            - name: XMPP_SERVER
              value: "prosody-service"
            - name: JICOFO_AUTH_USER
              value: "focus"
            - name: XMPP_BOSH_URL_BASE
              value: "/http-bind"
            - name: XMPP_AUTH_DOMAIN
              value: "auth.meet.jitsi"
            - name: XMPP_MUC_DOMAIN
              value: "muc.meet.jitsi"
            - name: TZ
              value: "Asia/Ho_Chi_Minh"
            - name: PUBLIC_URL
              value: ""
            - name: ENABLE_LETSENCRYPT
              value: "0"
            - name: ENABLE_HTTP_REDIRECT
              value: "0"
            - name: DISABLE_HTTPS
              value: "1"
            - name: ENABLE_HSTS
              value: "1"
            # Analytics and tracking (disabled)
            - name: AMPLITUDE_ID
              value: ""
            - name: ANALYTICS_SCRIPT_URLS
              value: ""
            - name: ANALYTICS_WHITELISTED_EVENTS
              value: ""
            - name: GOOGLE_ANALYTICS_ID
              value: ""
            # Features
            - name: ENABLE_GUESTS
              value: "1"
            - name: ENABLE_LOBBY
              value: "1"
            - name: ENABLE_AV_MODERATION
              value: "1"
            - name: ENABLE_BREAKOUT_ROOMS
              value: "1"
            - name: ENABLE_CALENDAR
              value: "0"
            - name: ENABLE_FILE_RECORDING_SHARING
              value: "0"
            - name: ENABLE_LIVE_STREAMING
              value: "0"
            - name: ENABLE_RECORDING
              value: "0"
            - name: ENABLE_REMB
              value: "1"
            - name: ENABLE_REQUIRE_DISPLAY_NAME
              value: "0"
            - name: ENABLE_SIMULCAST
              value: "1"
            - name: ENABLE_TCC
              value: "1"
            - name: ENABLE_TRANSCRIPTIONS
              value: "0"
            - name: ENABLE_WELCOME_PAGE
              value: "1"
            - name: ENABLE_CLOSE_PAGE
              value: "0"
            # UI Configuration
            - name: SHOW_CHROME_EXTENSION_BANNER
              value: "0"
            - name: SHOW_PROMOTIONAL_CLOSE_PAGE
              value: "0"
            - name: TOOLBAR_BUTTONS
              value: "microphone,camera,closedcaptions,desktop,fullscreen,fodeviceselection,hangup,profile,info,chat,recording,livestreaming,etherpad,sharedvideo,settings,raisehand,videoquality,filmstrip,invite,feedback,stats,shortcuts,tileview,videobackgroundblur,download,help,mute-everyone,security"
            # Branding
            - name: APP_NAME
              value: "Jitsi Meet"
            - name: BRAND_WATERMARK_LINK
              value: ""
            - name: DEFAULT_LANGUAGE
              value: "en"
            - name: DISABLE_VIDEO_BACKGROUND
              value: "0"
            # Mobile app configuration
            - name: MOBILE_APP_PROMO
              value: "0"
            - name: MOBILE_DOWNLOAD_LINK_ANDROID
              value: ""
            - name: MOBILE_DOWNLOAD_LINK_IOS
              value: ""
          resources:
            requests:
              memory: "512Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 80
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: jitsi-web-service
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: web
spec:
  ports:
    - port: 80
      targetPort: 80
      name: http
    - port: 443
      targetPort: 443
      name: https
  selector:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: web
  type: ClusterIP
