# Jitsi Meet Kubernetes Manifests

This directory contains all Kubernetes manifests needed to deploy Jitsi Meet on your cluster.

## Files Overview

| File | Description |
|------|-------------|
| `jitsi-secrets.yaml` | Secret containing authentication credentials |
| `prosody-config.yaml` | ConfigMap for Prosody XMPP server configuration |
| `prosody.yaml` | Prosody XMPP server deployment and service |
| `jicofo.yaml` | Jicofo (conference focus) deployment and service |
| `jvb.yaml` | Jitsi Videobridge deployment and services |
| `jitsi-web.yaml` | Web interface deployment and service |
| `ingress.yaml` | Traefik IngressRoute for external access |
| `jvb-hpa.yaml` | Horizontal Pod Autoscaler for JVB |
| `network-policy.yaml` | Network policies for security |
| `kustomization.yaml` | Kustomize configuration |
| `deploy-jitsi.sh` | Deployment script |

## Quick Deployment

### Using the deployment script (Recommended):
```bash
# Make script executable
chmod +x deploy-jitsi.sh

# Deploy with custom domain
./deploy-jitsi.sh deploy meet.yourcompany.com

# Check status
./deploy-jitsi.sh status

# View logs
./deploy-jitsi.sh logs
```

### Using kubectl directly:
```bash
# Apply all manifests
kubectl apply -f .

# Check deployment status
kubectl get pods -l app.kubernetes.io/name=jitsi

# Check services
kubectl get services -l app.kubernetes.io/name=jitsi
```

### Using Kustomize:
```bash
# Deploy using kustomize
kubectl apply -k .

# Or build and apply
kustomize build . | kubectl apply -f -
```

## Pre-deployment Configuration

### 1. Update Domain
Before deploying, update the domain in these files:
- `ingress.yaml`: Replace `meet.example.com` with your actual domain
- `jitsi-web.yaml`: Update `PUBLIC_URL` environment variable
- `prosody.yaml`: Update `PUBLIC_URL` environment variable

### 2. Update Secrets (IMPORTANT!)
The `jitsi-secrets.yaml` file contains default base64-encoded passwords. **You MUST change these in production!**

Generate new secrets:
```bash
echo -n "your-new-password" | base64
```

Replace the values in `jitsi-secrets.yaml` with your generated secrets.

### 3. Configure Public IP for JVB
Update the `DOCKER_HOST_ADDRESS` in `jvb.yaml` with your cluster's public IP address.

## Resource Requirements

| Component | CPU Request | Memory Request | CPU Limit | Memory Limit |
|-----------|-------------|----------------|-----------|--------------|
| Prosody | 200m | 512Mi | 500m | 1Gi |
| Jicofo | 300m | 768Mi | 1000m | 2Gi |
| JVB | 1000m | 2Gi | 2000m | 4Gi |
| Web | 200m | 512Mi | 500m | 1Gi |

## Networking

### Internal Ports
- Prosody: 5222 (XMPP), 5280 (BOSH), 5347 (Component)
- Jicofo: 8888 (HTTP API)
- JVB: 8080 (HTTP API), 10000 (UDP RTP)
- Web: 80 (HTTP), 443 (HTTPS)

### External Access
- Web Interface: https://your-domain.com
- XMPP WebSocket: https://your-domain.com/xmpp-websocket
- Colibri WebSocket: https://your-domain.com/colibri-ws/

## Scaling

### JVB Auto-scaling
The HPA is configured to scale JVB instances based on:
- CPU utilization: 70%
- Memory utilization: 80%
- Min replicas: 2
- Max replicas: 10

### Manual Scaling
```bash
# Scale JVB
kubectl scale deployment jitsi-videobridge --replicas=5

# Scale Web interface
kubectl scale deployment jitsi-web --replicas=3
```

## Monitoring

### Health Checks
All components have liveness and readiness probes configured.

### Logs
```bash
# View all Jitsi logs
kubectl logs -l app.kubernetes.io/name=jitsi --tail=100

# View specific component logs
kubectl logs -l app.kubernetes.io/component=jvb
kubectl logs -l app.kubernetes.io/component=prosody
kubectl logs -l app.kubernetes.io/component=jicofo
kubectl logs -l app.kubernetes.io/component=web
```

## Troubleshooting

### Common Issues

1. **Pods not starting**: Check resource availability and node capacity
2. **Authentication errors**: Verify secrets are correctly configured
3. **Network connectivity**: Check service discovery and network policies
4. **External access**: Verify Traefik configuration and DNS records

### Debug Commands
```bash
# Check pod status
kubectl describe pod <pod-name>

# Check service endpoints
kubectl get endpoints

# Test internal connectivity
kubectl exec -it <prosody-pod> -- nc -zv jicofo-service 8888

# Check Traefik routes
kubectl get ingressroute
```

### Port Forward for Testing
```bash
# Access Jitsi Web locally
kubectl port-forward service/jitsi-web-service 8080:80

# Access Prosody BOSH
kubectl port-forward service/prosody-service 5280:5280
```

## Security Considerations

1. **Change default secrets** before production deployment
2. **Configure network policies** to restrict unnecessary traffic
3. **Use TLS/SSL** for all external communications
4. **Regular updates** of container images
5. **Monitor access logs** for suspicious activity

## Integration with Existing Infrastructure

### PostgreSQL Integration
This deployment is designed to work with existing PostgreSQL infrastructure. No additional database configuration is required as Jitsi components use internal storage by default.

### Keycloak SSO Integration
For SSO integration with Keycloak, additional configuration is needed:
1. Configure JWT authentication in Jitsi
2. Set up OIDC client in Keycloak
3. Update Prosody configuration for external authentication

### Monitoring Integration
To integrate with existing monitoring:
1. Add Jitsi metrics to Prometheus targets
2. Create Grafana dashboards
3. Configure alerts in AlertManager

## Cleanup

### Using the script:
```bash
./deploy-jitsi.sh cleanup
```

### Manual cleanup:
```bash
kubectl delete -f .
# Or using kustomize
kubectl delete -k .
```

---

For more detailed information, refer to the main [README.md](../../../README.md) file.
