#!/bin/bash

# Script để deploy Jitsi với hỗ trợ LAN access và fix WebSocket
# Author: OSP Group
# Date: $(date)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if KUBECONFIG is set
if [ -z "$KUBECONFIG" ]; then
    print_error "KUBECONFIG is not set. Please run:"
    echo "export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config"
    exit 1
fi

# Function to deploy Jitsi with LAN support
deploy_jitsi() {
    print_status "Deploying Jitsi with LAN access support..."
    
    # Apply all configurations
    kubectl apply -f jitsi-secrets.yaml
    kubectl apply -f prosody-config.yaml
    kubectl apply -f prosody.yaml
    kubectl apply -f jicofo.yaml
    kubectl apply -f jvb.yaml
    kubectl apply -f jitsi-web.yaml
    kubectl apply -f ingress.yaml
    kubectl apply -f jvb-hpa.yaml
    kubectl apply -f network-policy.yaml
    
    print_success "Jitsi deployed successfully!"
}

# Function to check deployment status
check_status() {
    print_status "Checking deployment status..."
    
    echo -e "\n${BLUE}Pods status:${NC}"
    kubectl get pods -n jitsi-server
    
    echo -e "\n${BLUE}Services status:${NC}"
    kubectl get svc -n jitsi-server
    
    echo -e "\n${BLUE}IngressRoutes status:${NC}"
    kubectl get ingressroute -n jitsi-server
    
    echo -e "\n${BLUE}Node IPs:${NC}"
    kubectl get nodes -o wide | grep -E "NAME|192.168.1"
}

# Function to show access information
show_access_info() {
    print_success "Jitsi Access Information:"
    
    echo -e "\n${GREEN}1. External Access (Internet):${NC}"
    echo "   URL: https://jitsi.osp.vn"
    
    echo -e "\n${GREEN}2. LAN Access (Local Network):${NC}"
    echo "   Method 1 - Via Traefik (Recommended):"
    echo "   - http://************ (master node)"
    echo "   - http://************ (warehouse01 node)"  
    echo "   - http://************ (warehouse03 node)"
    
    echo -e "\n   Method 2 - Direct NodePort:"
    echo "   - http://************:30081"
    echo "   - http://************:30081"
    echo "   - http://************:30081"
    
    echo -e "\n${GREEN}3. WebSocket Endpoints:${NC}"
    echo "   - XMPP WebSocket: /xmpp-websocket"
    echo "   - Colibri WebSocket: /colibri-ws/"
    echo "   - BOSH: /http-bind"
    
    echo -e "\n${GREEN}4. UDP Ports for Media:${NC}"
    echo "   - JVB UDP: 30000 (NodePort)"
    echo "   - Internal: 10000"
    
    print_warning "Note: For LAN access, make sure your firewall allows:"
    echo "   - HTTP traffic on port 80 (Traefik)"
    echo "   - HTTP traffic on port 30080 (NodePort)"
    echo "   - UDP traffic on port 30000 (JVB media)"
}

# Function to test WebSocket connectivity
test_websocket() {
    print_status "Testing WebSocket connectivity..."
    
    # Test external WebSocket
    echo -e "\n${BLUE}Testing external WebSocket:${NC}"
    if command -v wscat &> /dev/null; then
        timeout 5 wscat -c wss://jitsi.osp.vn/xmpp-websocket || print_warning "External WebSocket test failed or timed out"
    else
        print_warning "wscat not installed. Install with: npm install -g wscat"
    fi
    
    # Test internal connectivity
    echo -e "\n${BLUE}Testing internal connectivity:${NC}"
    kubectl exec -n jitsi-server deployment/prosody -- nc -zv prosody-service 5280 || print_warning "Internal connectivity test failed"
}

# Function to show logs
show_logs() {
    local component=$1
    if [ -z "$component" ]; then
        print_error "Please specify component: prosody, jicofo, jvb, or web"
        return 1
    fi
    
    print_status "Showing logs for $component..."
    kubectl logs -n jitsi-server -l app.kubernetes.io/component=$component --tail=50 -f
}

# Function to cleanup
cleanup() {
    print_warning "This will delete all Jitsi resources. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Cleaning up Jitsi deployment..."
        kubectl delete -f . || true
        print_success "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Main script logic
case "$1" in
    "deploy")
        deploy_jitsi
        sleep 10
        check_status
        show_access_info
        ;;
    "status")
        check_status
        ;;
    "info")
        show_access_info
        ;;
    "test")
        test_websocket
        ;;
    "logs")
        show_logs "$2"
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "Usage: $0 {deploy|status|info|test|logs <component>|cleanup}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy Jitsi with LAN access support"
        echo "  status   - Check deployment status"
        echo "  info     - Show access information"
        echo "  test     - Test WebSocket connectivity"
        echo "  logs     - Show logs for component (prosody|jicofo|jvb|web)"
        echo "  cleanup  - Remove all Jitsi resources"
        echo ""
        echo "Example:"
        echo "  $0 deploy"
        echo "  $0 logs prosody"
        exit 1
        ;;
esac
